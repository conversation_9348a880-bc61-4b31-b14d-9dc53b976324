# tk主界面上的置顶通知栏
import tkinter as tk
from tkinter import ttk

class Marquee(ttk.Frame):
    def __init__(self, parent, text, fps=30, step=2, font=("TkDefaultFont", 10), **kwargs):
        super().__init__(parent, **kwargs)
        self.fps = fps
        self.step = step
        self.font = font
        # 根据字体大小计算固定高度
        font_name, font_size = font

        self.canvas_height = font_size + 8

        self.canvas = tk.Canvas(self,height=self.canvas_height,highlightthickness=0)
        self.canvas.pack(fill="x", side="left", expand=True)
        self.canvas.bind("<Configure>", self._on_canvas_configure)
        # 创建文字时直接使用固定高度计算垂直位置
        self.text_id = self.canvas.create_text(2000,self.canvas_height // 2,  text=text,anchor="w",font=font,)

        # 布局完成后初始化
        self.after(100, self._reset_and_animate) # noqa

    def _on_canvas_configure(self, event):
        """当画布尺寸变化时，重置文字位置到右侧"""
        # 确保只处理一次有效尺寸变化
        if event.width > 10:  # 忽略初始化时的小尺寸
            self.canvas.coords(self.text_id, event.width, self.canvas_height // 2)

    def _reset_and_animate(self):
        # 只需设置水平位置（垂直位置已固定）
        # 如果尚未配置，确保位置正确
        if self.canvas.winfo_width() > 10:
            self.canvas.coords(self.text_id, self.canvas.winfo_width(), self.canvas_height // 2)
        self._animate()

    def _animate(self):
        self.canvas.move(self.text_id, -self.step, 0)
        x1, y1, x2, y2 = self.canvas.bbox(self.text_id)
        if x2 < 0:
            canvas_w = self.canvas.winfo_width()
            # 重置时仍使用固定高度
            self.canvas.coords(self.text_id, canvas_w, self.canvas_height // 2)
        self.after(int(1000 / self.fps), self._animate) # noqa



class NotificationBar(ttk.Frame):
    def __init__(self, parent, text, **kwargs):
        super().__init__(parent, **kwargs)
        # 滚动文字
        self.marquee = Marquee(self, text=text, font=("TkDefaultFont", 10))
        self.marquee.pack(side="left", fill="x", expand=True, padx=(5, 0), pady=2)

        # 关闭按钮
        self.close_btn = ttk.Button(self, text="✕", width=2, command=self._on_close, style="Close.TButton")
        style = ttk.Style(self)
        style.configure("Close.TButton", font=("TkDefaultFont", 4))
        self.close_btn.pack(side="right", padx=5, pady=2)

    def _on_close(self):
        # 隐藏自身，但保留父容器空间
        self.pack_forget()
