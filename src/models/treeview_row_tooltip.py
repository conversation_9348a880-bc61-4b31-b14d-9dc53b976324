# treeview行显示提示文本
import functools
import tkinter as tk
from tkinter import ttk

class TreeViewRowToolTip:
    """
    为TreeView的行添加简单的tooltip功能
    """
    def __init__(self, treeview: ttk.Treeview):
        self.treeview = treeview
        self.row_tooltips = {}  # 存储每行的tooltip文本
        self.tooltip_visible = False  # 跟踪tooltip是否可见
        self.tooltip_after_id = None  # 存储延迟显示的after ID

        # 确保使用正确的父窗口
        self.root = treeview.winfo_toplevel()

        # 创建独立的顶层窗口作为tooltip，而不是普通标签
        self.tooltip_window = tk.Toplevel(self.root)
        self.tooltip_window.withdraw()  # 初始隐藏
        self.tooltip_window.overrideredirect(True)  # 移除窗口边框
        self.tooltip_window.wm_attributes("-topmost", True)  # 保持在最上层

        # 在tooltip窗口中创建标签
        self.tooltip_label = tk.Label(
            self.tooltip_window,
            background="lightyellow",
            relief="solid",
            borderwidth=1,
            font=("Arial", 9),
            justify='left',
            padx=5,
            pady=3
        )
        self.tooltip_label.pack()

        # 绑定鼠标移动和离开事件
        self.treeview.bind("<Motion>", self.on_motion)
        self.treeview.bind("<Leave>", self.on_leave)
        # 添加绑定处理滚动
        self.treeview.bind("<MouseWheel>", self.on_leave)
        self.treeview.bind("<Button-4>", self.on_leave)
        self.treeview.bind("<Button-5>", self.on_leave)
        # 添加焦点变化事件
        self.treeview.bind("<FocusOut>", self.on_leave)

        self.last_item = None  # 记录上一次鼠标移动到的行

    def set_row_tooltip(self, item_id, tooltip_text):
        """为指定行设置tooltip文本"""
        # 确保键是字符串类型，与TreeView的identify_row返回类型一致
        self.row_tooltips[str(item_id)] = tooltip_text

    def clear_all_row_tooltips(self):
        """清除所有行的tooltip"""
        self.row_tooltips.clear()

    def on_motion(self, event):
        """鼠标移动事件处理"""
        # 取消之前的延迟显示
        if self.tooltip_after_id:
            self.treeview.after_cancel(self.tooltip_after_id)
            self.tooltip_after_id = None

        # 获取当前鼠标下的行
        item = self.treeview.identify_row(event.y)

        if not item or item not in self.row_tooltips:
            self.hide_tooltip()
            self.last_item = None
            return

        if item == self.last_item and self.tooltip_visible:
            return  # 鼠标移动到同一行且tooltip已显示，忽略

        self.last_item = item

        # 隐藏之前的tooltip
        self.hide_tooltip()

        # 延迟300毫秒显示tooltip，避免快速移动时频繁显示
        tooltip_text = self.row_tooltips[item]
        if tooltip_text:  # 确保有文本才显示
            show_text=functools.partial(self.show_tooltip,event=event,text=tooltip_text)
            self.tooltip_after_id = self.treeview.after(300, show_text) # noqa

    def on_leave(self, event=None): # noqa
        """鼠标离开TreeView时隐藏tooltip"""
        # 取消任何待处理的tooltip显示
        if self.tooltip_after_id:
            self.treeview.after_cancel(self.tooltip_after_id)
            self.tooltip_after_id = None

        self.hide_tooltip()
        self.last_item = None

    def show_tooltip(self, event, text):
        """显示tooltip"""
        if not text:
            return

        # 设置tooltip文本
        self.tooltip_label.config(text=text)

        # 更新窗口以确保获得正确尺寸
        self.tooltip_window.update_idletasks()

        # 计算位置（相对于屏幕）
        x = event.x_root + 15
        y = event.y_root + 10

        # 防止tooltip超出屏幕
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        tooltip_width = self.tooltip_window.winfo_reqwidth()
        tooltip_height = self.tooltip_window.winfo_reqheight()

        # 水平方向调整
        if x + tooltip_width > screen_width:
            x = screen_width - tooltip_width - 10

        # 垂直方向调整
        if y + tooltip_height > screen_height:
            y = screen_height - tooltip_height - 10

        # 设置tooltip窗口位置并显示
        self.tooltip_window.geometry(f"+{x}+{y}")
        self.tooltip_window.deiconify()
        self.tooltip_visible = True

    def hide_tooltip(self):
        """隐藏tooltip"""
        self.tooltip_window.withdraw()
        self.tooltip_visible = False
