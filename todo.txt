铁路沿线  https://www.aliyundrive.com/s/riWx5h9X3vJ
激流中国 

将所有需要的学习资料复制到硬盘柜中 --- 本地的，U盘内的，云盘内的
所有待做事项迁移到本地

统一gui界面控件字体
悬停行显示的路径的"径"字体很怪

全流程测试，混合命令行gui+命令行server+idea运行

配置选择界面中显示功能需要按照md文档来，其排序需要按照最后使用时间排序

性能优化-1：
配置加载选择界面点击配置记录选择加载主界面慢

_wait_for_shutdown中为什么需要设置0.4才不报错，实际测试 cleanup_on_shutdown 执行不需要0.02 s


参考 https://github.com/nmhjklnm/sms_server 项目进行功能补充和优化

所以的import都需要加上代码文件开头,避免引入时出现循环导入的情况  本项目的import后只能是文件名不能是类名称
删除完全没有使用的函数方法和注释掉的代码
使用ai，给代码文件重命名,调整代码函数在文件内的顺序:__init__函数在最上面,只在文件内使用的函数第二,静态函数中间,可以给外部使用的函数在最后
将代码中注释的注意事项和根据代码分析得到的注意事项总结成说明文档

python D:\Git\python-samples-hub\src\config_selection_gui.py
python D:\Git\python-samples-hub\src\webhook_server_gui.py --config C:\Users\<USER>\.webhook_server\server_config_4.ini
python D:\Git\python-samples-hub\src\webhook_server_command.py --config C:\Users\<USER>\.webhook_server\server_config_4.ini

